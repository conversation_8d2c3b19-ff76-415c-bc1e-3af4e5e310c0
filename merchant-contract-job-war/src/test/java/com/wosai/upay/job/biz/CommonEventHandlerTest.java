package com.wosai.upay.job.biz;

import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.sales.service.MerchantEnrolService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/7/16 12:35
 * @Description:
 */
@Slf4j
public class CommonEventHandlerTest extends BaseTest {

    @Autowired
    private CommonEventHandler commonEventHandler;
    @Autowired
    private ContractEventMapper contractEventMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractSubTaskHandler contractSubTaskHandler;
    @Autowired
    MerchantEnrolService merchantEnrolService;
    @Autowired
    ProviderTradeParamsService providerTradeParamsService;


    @Test
    public void handleInsert() throws Exception {
        try {
            String merchantSn = "21680002920681";
            ContractTask task = contractTaskMapper.selectForTipsByMerchantSn(merchantSn);
            List<ContractSubTask> contractSubTaskList = contractSubTaskMapper
                    .selectByMerchantSnAndPTaskIdAndScheduleStatus(merchantSn, task.getId(), 1);
            for (ContractSubTask contractSubTask : contractSubTaskList) {
                if (ProviderUtil.LKL_PROVIDER_CHANNEL.equals(contractSubTask.getChannel()) && !StringUtils.isEmpty(contractSubTask.getContract_id()) && !"resubmit".equals(contractSubTask.getContract_id())) {
                    //拉卡拉请求等待回调
                    continue;
                }
                contractSubTaskHandler.handle(task, contractSubTask);
            }
            Thread.sleep(5000);
            List<ContractSubTask> contractSubTaskList2 = contractSubTaskMapper
                    .selectByMerchantSnAndPTaskIdAndScheduleStatus(merchantSn, task.getId(), 1);
            for (ContractSubTask contractSubTask : contractSubTaskList2) {
                if (ProviderUtil.LKL_PROVIDER_CHANNEL.equals(contractSubTask.getChannel()) && !StringUtils.isEmpty(contractSubTask.getContract_id()) && !"resubmit".equals(contractSubTask.getContract_id())) {
                    //拉卡拉请求等待回调
                    continue;
                }
                contractSubTaskHandler.handle(task, contractSubTask);
            }
        } catch (Throwable t) {

        }

    }

    @Test
    public void handleUpdate() {
        ContractEvent event = new ContractEvent().setEvent_type(4).setMerchant_sn("21690003003669");
        try {
            commonEventHandler.handle(event);

        } catch (Throwable t) {
        }

    }

    @Test
    public void handleMerchant() throws Exception {
        try {
            doHandle("21680002929409");

        } catch (Throwable t) {
        }
    }


    @Test
    public void testParamTime() throws Exception {
        String merchantSn = "21680002929616";
        Boolean newRule = Boolean.FALSE;
        Boolean weixinParamNew = Boolean.FALSE;
        try {
            newRule = merchantEnrolService.invokeNewEnrolRule(merchantSn);
        } catch (Exception e) {
            log.error(" {} 获取灰度新规则失败", merchantSn, e);
        }

        Map query = Maps.newHashMap();
        query.put("merchant_sn", merchantSn);
        query.put("channel_no", "36002013293");
        query.put("payway", 3);
        ListResult providerParams = providerTradeParamsService.listMerchantProviderParams(null, query);
        if (providerParams != null && !CollectionUtils.isEmpty(providerParams.getRecords())) {
            Long cTime = BeanUtil.getPropLong(providerParams.getRecords().get(0), DaoConstants.CTIME);
        }

    }

    @Test
    public void testAuth() throws Exception {
        //王斌
        Long eventId = 52998L;
        //张雁
//        Long eventId = 73L;
        ContractEvent event = contractEventMapper.selectByPrimaryKey(eventId);
        String merchantSn = event.getMerchant_sn();
        commonEventHandler.handle(event);
        Thread.sleep(5000);
        doHandle(merchantSn);
        Thread.sleep(5000);
        doHandle(merchantSn);
        System.out.println("sssssssss");
    }

    private void doHandle(String merchantSn) throws Exception {
        ContractTask task = contractTaskMapper.selectForTipsByMerchantSn(merchantSn);
        List<ContractSubTask> contractSubTaskList = contractSubTaskMapper
                .selectByMerchantSnAndPTaskIdAndScheduleStatus(merchantSn, task.getId(), 1);
        for (ContractSubTask contractSubTask : contractSubTaskList) {
            if (ProviderUtil.LKL_PROVIDER_CHANNEL.equals(contractSubTask.getChannel()) && !StringUtils.isEmpty(contractSubTask.getContract_id()) && !"resubmit".equals(contractSubTask.getContract_id())) {
                //拉卡拉请求等待回调
                continue;
            }
            contractSubTaskHandler.handle(task, contractSubTask);
        }
    }
}
