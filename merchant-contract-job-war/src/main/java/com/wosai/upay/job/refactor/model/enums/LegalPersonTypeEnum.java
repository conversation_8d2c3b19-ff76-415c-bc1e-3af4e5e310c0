package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 法人类型枚举
 *
 * <AUTHOR>
 */
public enum LegalPersonTypeEnum implements ITextValueEnum<Integer> {
    /**
     * 非法人
     */
    NOT_LEGAL_PERSON(0, "非法人"),
    /**
     * 法人
     */
    LEGAL_PERSON(1, "法人");

    private final int value;
    private final String text;

    LegalPersonTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
