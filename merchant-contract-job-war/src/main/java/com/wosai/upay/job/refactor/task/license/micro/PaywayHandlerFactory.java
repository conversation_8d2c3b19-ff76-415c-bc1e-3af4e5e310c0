package com.wosai.upay.job.refactor.task.license.micro;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.model.Merchant;
import com.wosai.upay.job.model.MerchantProviderParams;
import com.wosai.upay.job.model.MerchantProviderParamsDO;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.wosai.upay.job.refactor.task.license.micro.AbstractUpdateTradeParamsTemplate.AUTH_TIME;

/**
 * 支付方式参数处理器工厂
 * 提供通用的支付方式处理逻辑，减少重复代码
 * 
 * <AUTHOR> Assistant
 * @date 2025/07/11
 */
@Component
@Slf4j
public class PaywayHandlerFactory {

    /**
     * 创建通用的支付方式处理器
     * 适用于大部分收单机构的标准处理逻辑
     */
    public static AbstractUpdateTradeParamsTemplate.PaywayParamHandler createStandardHandler() {
        return (params, newParam, oldNewParamsMap, acquireInfo, merchant, wxTime, aliTime) -> {
            if (Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                return handleAcquirerPayway(newParam, acquireInfo);
            } else if (Objects.equals(params.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                return handleAlipayPayway(params, newParam, oldNewParamsMap, acquireInfo, merchant, aliTime);
            } else if (Objects.equals(params.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                return handleWeixinPayway(params, newParam, oldNewParamsMap, acquireInfo, wxTime);
            } else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                return handleUnionpayPayway(params, newParam, oldNewParamsMap, acquireInfo);
            }
            return false;
        };
    }

    /**
     * 处理收单机构支付方式
     */
    private static boolean handleAcquirerPayway(
            MerchantProviderParamsDO newParam, 
            MerchantAcquireInfoBO acquireInfo) {
        newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
        newParam.setPayMerchantId(acquireInfo.getAcquireMerchantId());
        return false;
    }

    /**
     * 处理支付宝支付方式
     */
    private static boolean handleAlipayPayway(
            MerchantProviderParamsDO params,
            MerchantProviderParamsDO newParam,
            Map<String, MerchantProviderParamsDO> oldNewParamsMap,
            MerchantAcquireInfoBO acquireInfo,
            Map<String, Object> merchant,
            Long aliTime) {
        
        // 检查是否已存在相同的支付宝参数
        boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
            Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue()) && 
            Objects.equals(param.getPayMerchantId(), acquireInfo.getAliNo()));
        if (present) return true;
        
        newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
        newParam.setPayMerchantId(acquireInfo.getAliNo());
        
        // 设置支付宝MCC
        String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
        // 注意：这里需要注入industryMappingCommonBiz，暂时注释
        // String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
        // newParam.setAliMcc(aliMcc);
        
        newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME, aliTime)));
        return false;
    }

    /**
     * 处理微信支付方式
     */
    private static boolean handleWeixinPayway(
            MerchantProviderParamsDO params,
            MerchantProviderParamsDO newParam,
            Map<String, MerchantProviderParamsDO> oldNewParamsMap,
            MerchantAcquireInfoBO acquireInfo,
            String wxTime) {
        
        // 检查是否已存在相同的微信参数
        boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
            Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()) && 
            Objects.equals(param.getPayMerchantId(), acquireInfo.getWxNo()));
        if (present) return true;
        
        newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
        newParam.setPayMerchantId(acquireInfo.getWxNo());
        newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME, wxTime)));
        return false;
    }

    /**
     * 处理银联支付方式
     */
    private static boolean handleUnionpayPayway(
            MerchantProviderParamsDO params,
            MerchantProviderParamsDO newParam,
            Map<String, MerchantProviderParamsDO> oldNewParamsMap,
            MerchantAcquireInfoBO acquireInfo) {
        
        // 检查是否已存在相同的银联参数
        boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
            Objects.equals(param.getPayway(), PaywayEnum.UNIONPAY.getValue()) && 
            Objects.equals(param.getPayMerchantId(), acquireInfo.getUnionNo()));
        if (present) return true;
        
        newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
        newParam.setPayMerchantId(acquireInfo.getUnionNo());
        return false;
    }

    /**
     * 创建拉卡拉V3特定的处理器
     */
    public static AbstractUpdateTradeParamsTemplate.PaywayParamHandler createLklV3Handler() {
        return (params, newParam, oldNewParamsMap, acquireInfo, merchant, wxTime, aliTime) -> {
            if (Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                newParam.setProviderMerchantId(acquireInfo.getUnionNo());
                newParam.setPayMerchantId(acquireInfo.getAcquireMerchantId());
            } else if (Objects.equals(params.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
                    Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue()) && 
                    Objects.equals(param.getPayMerchantId(), acquireInfo.getAliNo()));
                if (present) return true;
                
                newParam.setProviderMerchantId(acquireInfo.getUnionNo());
                newParam.setPayMerchantId(acquireInfo.getAliNo());
                
                String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
                // 注意：这里需要注入industryMappingCommonBiz
                // String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
                // newParam.setAliMcc(aliMcc);
                
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME, aliTime)));
            } else if (Objects.equals(params.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
                    Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()) && 
                    Objects.equals(param.getPayMerchantId(), acquireInfo.getWxNo()));
                if (present) return true;
                
                newParam.setProviderMerchantId(acquireInfo.getUnionNo());
                newParam.setPayMerchantId(acquireInfo.getWxNo());
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME, wxTime)));
            } else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
                    Objects.equals(param.getPayway(), PaywayEnum.UNIONPAY.getValue()) && 
                    Objects.equals(param.getPayMerchantId(), acquireInfo.getUnionNo()));
                if (present) return true;
                
                newParam.setProviderMerchantId(acquireInfo.getUnionNo());
                newParam.setPayMerchantId(acquireInfo.getUnionNo());
            }
            return false;
        };
    }

    /**
     * 创建海科特定的处理器
     */
    public static AbstractUpdateTradeParamsTemplate.PaywayParamHandler createHaikeHandler() {
        return (params, newParam, oldNewParamsMap, acquireInfo, merchant, wxTime, aliTime) -> {
            if (Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
                newParam.setPayMerchantId(acquireInfo.getAcquireMerchantId());
            } else if (Objects.equals(params.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
                    Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue()) && 
                    Objects.equals(param.getPayMerchantId(), acquireInfo.getAliNo()));
                if (present) return true;
                
                newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
                newParam.setPayMerchantId(acquireInfo.getAliNo());
                newParam.setMerchantName(acquireInfo.getHaikeAliName());
                
                String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
                // String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
                // newParam.setAliMcc(aliMcc);
                
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME, aliTime)));
            } else if (Objects.equals(params.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
                    Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()) && 
                    Objects.equals(param.getPayMerchantId(), acquireInfo.getWxNo()));
                if (present) return true;
                
                newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
                newParam.setPayMerchantId(acquireInfo.getWxNo());
                newParam.setMerchantName(acquireInfo.getHaikeWxName());
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME, wxTime)));
            } else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                boolean present = oldNewParamsMap.values().stream().anyMatch(param -> 
                    Objects.equals(param.getPayway(), PaywayEnum.UNIONPAY.getValue()) && 
                    Objects.equals(param.getPayMerchantId(), acquireInfo.getUnionNo()));
                if (present) return true;
                
                newParam.setProviderMerchantId(acquireInfo.getAcquireMerchantId());
                newParam.setMerchantName(acquireInfo.getHaikeUnionName());
                newParam.setPayMerchantId(acquireInfo.getUnionNo());
                
                // 海科特有的额外参数设置
                HashMap<String, Object> extra = new HashMap<>();
                extra.put("tradeParams", CollectionUtil.hashMap("bankMerchNo", acquireInfo.getUnionNo()));
                newParam.setExtra(JSON.toJSONString(extra));
            }
            return false;
        };
    }
}
