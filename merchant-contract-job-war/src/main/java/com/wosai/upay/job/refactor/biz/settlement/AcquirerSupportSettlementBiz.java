package com.wosai.upay.job.refactor.biz.settlement;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.refactor.dao.AcquirerSupportSettlementDAO;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.AcquirerSupportSettlementDO;
import com.wosai.upay.job.refactor.model.enums.MerchantSettlementAccountTypeEnum;
import com.wosai.upay.job.refactor.model.enums.SupportTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.EnumMap;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;


/**
 * 收单机构支持结算类型表表Service层 {@link AcquirerSupportSettlementDO}
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AcquirerSupportSettlementBiz {

    @Resource
    private AcquirerSupportSettlementDAO acquirerSupportSettlementDAO;

    private static final EnumMap<MerchantSettlementAccountTypeEnum, Function<AcquirerSupportSettlementDO, Boolean>> settlementFunctionMap = Maps.newEnumMap(MerchantSettlementAccountTypeEnum.class);


    static {
        settlementFunctionMap.put(MerchantSettlementAccountTypeEnum.LEGAL_PRIVATE, acquirerSupportSettlementDO
                -> Objects.equals(acquirerSupportSettlementDO.getLegalPrivateSupportType(), SupportTypeEnum.YES.getValue()));
        settlementFunctionMap.put(MerchantSettlementAccountTypeEnum.NON_LEGAL_PRIVATE, acquirerSupportSettlementDO
                -> Objects.equals(acquirerSupportSettlementDO.getNonLegalPrivateSupportType(), SupportTypeEnum.YES.getValue()));
        settlementFunctionMap.put(MerchantSettlementAccountTypeEnum.COMMON_PUBLIC, acquirerSupportSettlementDO
                -> Objects.equals(acquirerSupportSettlementDO.getCommonPublicSupportType(), SupportTypeEnum.YES.getValue()));
        settlementFunctionMap.put(MerchantSettlementAccountTypeEnum.OTHER_PUBLIC, acquirerSupportSettlementDO
                -> Objects.equals(acquirerSupportSettlementDO.getOtherPublicSupportType(), SupportTypeEnum.YES.getValue()));
        settlementFunctionMap.put(MerchantSettlementAccountTypeEnum.OTHER, acquirerSupportSettlementDO -> false);
    }


    /**
     * 根据商户特征,校验是否符合对应收单机构支持的账户结算类型
     *
     * @param merchantFeatureBO 商户特征
     * @param acquirer          收单机构
     * @return 校验结果
     */
    public ContractGroupRuleVerifyResultBO verifySettlementAccount(MerchantFeatureBO merchantFeatureBO, String acquirer) {
        if (Objects.isNull(merchantFeatureBO) || StringUtils.isBlank(acquirer) || Objects.isNull(merchantFeatureBO.getExtraFeature())) {
            log.error("参数异常,收单机构: {}, 商户特征: {}", acquirer, JSON.toJSONString(merchantFeatureBO));
            throw new CommonPubBizException("参数异常,商户特征或者收单机构为空");
        }
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.getExtraFeature();
        MerchantBusinessLicenseInfo licenseInfo = extraFeature.getMerchantBusinessLicenseInfo();
        if (StringUtils.isBlank(merchantFeatureBO.getSettlementAccountType())) {
            log.warn("商户:{}, 账户结算类型为空", merchantFeatureBO.getMerchantSn());
            return new ContractGroupRuleVerifyResultBO(false, "账户结算类型为空");
        }
        MerchantSettlementAccountTypeEnum settleAccountType = EnumUtils.getEnum(MerchantSettlementAccountTypeEnum.class,
                Integer.valueOf(merchantFeatureBO.getSettlementAccountType()));
        Optional<AcquirerSupportSettlementDO> supportSettlementOptional = acquirerSupportSettlementDAO.getByAcquirerAndLicenseType(acquirer, licenseInfo.getType());
        String licenceTypeText = EnumUtils.ofNullable(BusinessLicenseTypeEnum.class, licenseInfo.getType()).map(BusinessLicenseTypeEnum::getText).orElse("未知营业执照类型");
        if (!supportSettlementOptional.isPresent()) {
            String message = String.format("该收单机构对应的营业执照类型,未维护支持的结算账户类型. 收单机构:%s, 商户营业执照类型:%s", acquirer, licenceTypeText);
            log.warn(message);
            return new ContractGroupRuleVerifyResultBO(true);
        }
        Boolean verifyResult = settlementFunctionMap.get(settleAccountType).apply(supportSettlementOptional.get());
        ContractGroupRuleVerifyResultBO verifyResultBO = new ContractGroupRuleVerifyResultBO(verifyResult);
        if (!verifyResult) {
            String message = String.format("营业执照类型对应的账户结算类型校验未通过,商户:%s,营业执照类型:%s,账户结算类型:%s,对应收单机构:%s",
                    merchantFeatureBO.getMerchantSn(), licenceTypeText, settleAccountType.getText(), acquirer);
            log.warn(message);
            verifyResultBO.setMessage(message);
        }
        return verifyResultBO;
    }
}
