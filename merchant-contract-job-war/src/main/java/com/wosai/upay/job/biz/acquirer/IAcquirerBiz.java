package com.wosai.upay.job.biz.acquirer;

import com.beust.jcommander.internal.Lists;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.acquirer.SyncSubMchIdStatusResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateParam;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateQueryResponse;
import com.wosai.upay.merchant.contract.model.weixin.ApplySpecialFeeRateResponse;
import com.wosai.upay.merchant.contract.model.weixin.ModifySpecialFeeRateParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-04-16
 */
public interface IAcquirerBiz {

    /**
     * 获取商户默认的报备规则组
     *
     * @param merchantSn
     * @return
     */
    default String getMerchantDefaultRuleGroup(String merchantSn, String acquirer) {
        return getDefaultRuleGroup(acquirer);
    }

    /**
     * 获取收单机构默认的报备规则组
     *
     * @deprecated {@link AcquirerSharedAbility#getDefaultContractRuleGroupId()}
     */
    @Deprecated
    String getDefaultRuleGroup(String acquirer);


    /**
     * 从 payway == null 的 merchant_config 中获取子商户号
     *
     * @param merchantConfig
     * @return
     */
    String getAcquirerMchIdFromMerchantConfig(Map merchantConfig);

    /**
     * 将商户状态同步到收单机构
     *
     * @param merchantSn
     * @param status     0：关闭  1：正常  2：禁用
     * @return
     */
    default SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        return new SyncMchStatusResp().setSuccess(true).setMessage("success");
    }

    /**
     * 将该收单机构下的微信和支付宝子商户号状态同步到银联
     *
     * @param merchantSn 商户号
     * @param status     0：关闭 1：正常 2：禁用
     * @return 同步结果
     */
    default List<SyncSubMchIdStatusResp> syncSubMchIdStatus(String merchantSn, int status) {
        return Lists.newArrayList();
    }

    /**
     * 将商户状态同步到收单机构 & 将该收单机构下的微信和支付宝子商户号状态同步到银联
     *
     * @param merchantSn 商户号
     * @param status     0：关闭 1：正常 2：禁用
     * @return 同步结果
     */
    default SyncMchStatusResp syncMchAndSubMchIdStatus(String merchantSn, int status) {
        SyncMchStatusResp syncMchStatusResp = syncMchStatusToAcquirer(merchantSn, status);
        if (syncMchStatusResp.isSuccess()) {
            syncMchStatusResp.setSubMchIdSyncResult(syncSubMchIdStatus(merchantSn, status));
        }
        return syncMchStatusResp;
    }

    /**
     * 获取商户微信普通渠道规则
     *
     * @return
     */
    default String getMerchantNormalWxRule(String merchantSn) {
        return getNormalWxRule();
    }

    /**
     * 获取收单机构默认微信普通渠道规则
     *
     * @return
     */
    String getNormalWxRule();

    /**
     * 向微信更新客服电话和经营名称
     *
     * @param paramsDto
     * @param params
     */
    default void updateWeixinParams(MerchantProviderParamsDto paramsDto, Map params) {
        throw new ContractBizException("商户所在收单机构不支持向微信更新客服电话和经营名称操作");
    }


    /**
     * 向支付宝更新商户名称
     *
     * @param params
     * @param alipayMchInfo
     */
    default ContractResponse updateAlipayParams(MerchantProviderParams params, AlipayMchInfo alipayMchInfo) {
        throw new ContractBizException("商户所在收单机构不支持向向支付宝更新商户名称");
    }

    /**
     * 更新费率
     *
     * @param merchantSn
     * @param param
     * @param contractChannel
     * @return
     */
    default ApplySpecialFeeRateResponse modifyFeeRate(String merchantSn, ModifySpecialFeeRateParam param, ContractChannel contractChannel) {
        throw new ContractBizException("商户所在收单机构不支持更新费率操作");
    }

    /**
     * 申请费率
     *
     * @param merchantSn
     * @param param
     * @param contractChannel
     * @return
     */
    default ApplySpecialFeeRateResponse applyFeeRate(String merchantSn, ApplySpecialFeeRateParam param, ContractChannel contractChannel) {
        throw new ContractBizException("商户所在收单机构不支持费率申请操作");
    }

    /**
     * 查询费率申请状态
     *
     * @param merchantSn
     * @param applicationId
     * @param contractChannel
     * @return
     */
    default ApplySpecialFeeRateQueryResponse queryRateApplyStatus(String merchantSn, String applicationId, ContractChannel contractChannel) throws Exception {
        throw new ContractBizException("商户所在收单机构不支持查询费率申请状态操作");
    }


    /**
     * 获取商户银联参数
     *
     * @param merchantSn
     * @return
     */
    default Map<String, Object> getUnionOpenParam(String merchantSn) {
        return null;
    }


    /**
     * 微信高校食堂 获取商户 渠道号
     *
     * @return
     */
    default String getSchoolCanteenChannelNo(String merchantSn) {
        throw new ContractBizException("商户所在收单机构暂不支持微信高校食堂活动");
    }

    /**
     * 获取微信子商户号在微信侧的信息
     *
     * @param providerParams
     * @return
     */
    default WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        return new WxMchInfo();
    }

    /**
     * 获取支付宝子商户号在支付宝侧的信息
     *
     * @param providerParams
     * @return
     */
    default AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        return new AlipayMchInfo();
    }

    /**
     * 向支付源同步商户信息
     *
     * @param merchantSn
     * @param payWay     2-支付宝 3-微信
     */
    default void syncMchInfo2PayWay(String merchantSn, int payWay) {
        throw new ContractBizException("商户所在收单机构不支持向支付源同步信息");
    }

    /**
     * 修改微信子商户号 名称 + settlementId
     * 由于微信提供的 [修改settlementid 修改接口] 调用时间为每日11：30之前，不具备实际使用可能
     * 目前暂时不修改 settlementid
     * 因此：当判断 微信交易参数的 settlementid 与 规则不符合是，直接返回修改失败
     *
     * @param merchantProviderParams
     * @param context
     * @return true 修改成功 false 修改失败
     */
    default Boolean updateWechatNameAndSettleMentId(MerchantProviderParams merchantProviderParams, Map context) {
        return Boolean.FALSE;
    }

    /**
     * @param merchantSn 商户号
     * @return false- 间连收单机构商户状态关闭 true-间连收单机构商户状态开启
     * <AUTHOR>
     * @Description: 获取商户在收单机构的状态
     * @time 10:01
     */
    default Boolean getAcquirerMchStatus(String merchantSn) {
        return Boolean.TRUE;
    }

    /**
     * 收钱吧商户银行卡与商户在收单机构的银行卡是否一致
     *
     * @param merchantSn
     * @param bankAccount
     * @deprecated {@link com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility#isBankCardConsistentWithSqb(String)}
     */
    @Deprecated
    default Boolean bankAccountConsistent(String merchantSn, Map bankAccount) {
        return Boolean.TRUE;
    }


    /**
     * 商户银行卡和商户在收单机构状态是否一致
     *false- 银行卡或者状态和收单机构不同 true-银行卡或者状态和收单机构相同
     * @param merchantSn 商户号
     * @param bankAccount 银行卡信息
     * @return
     */
    default Boolean bankAccountConsistentAndStatusOpen(String merchantSn, Map bankAccount) {
        return Boolean.TRUE;
    }


    /**
     * 查询实时费率接口
     *
     * @param merchantSn
     * @return
     */
    default List<Map> queryFeeRate(String merchantSn) { return new ArrayList<>(); }

    /**
     * 查询当前收单机构下银联云闪付开通状态
     * @param merchantSn
     * @return
     */
     UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn);


    /**
     * 设置收单机构清算通道标志
     * @param merchantId
     */
     void updateClearanceProvider(String merchantId);

}
