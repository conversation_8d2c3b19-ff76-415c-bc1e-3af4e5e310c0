package com.wosai.upay.job.providers;

import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.model.dto.WeixinSubDevResp;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.terminal.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19/4/2
 */
public interface BasicProvider {

    /**
     * 生成报备子任务
     *
     * @param merchantSn
     * @param event
     * @param paramContext
     * @param contractRule
     * @return
     */
    ContractSubTask produceTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule);

    /**
     * 处理增网增终新增任务
     *
     * @param event
     * @return
     */
    void produceInsertTerminalTaskByRule(TerminalBasicInsertEvent event, MerchantInfo merchant);

    /**
     * 处理报备子任务
     *
     * @param contractTask
     * @param contractChannel
     * @param contractSubTask
     * @return
     */
    ContractResponse processTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask);

    /**
     * 微信子商户配置（支付目录、公众号）
     *
     * @param merchantProviderParams
     * @return
     */
    WeixinSubDevResp weixinSubDevConfig(MerchantProviderParams merchantProviderParams);

    /**
     * 微信子商户配置公众号
     *
     * @param appid
     * @param merchantProviderParams
     * @return
     */
    WeixinSubDevResp weixinSubDevConfig(String appid, MerchantProviderParams merchantProviderParams);

    /**
     * 微信子商户配置（支付目录、公众号）
     *
     * @param merchantProviderParams
     * @return
     */
    WeixinSubDevResp weixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams);

    /**
     * 切换交易参数
     *
     * @param merchantProviderParams
     * @param fee
     * @param sync
     * @param tradeAppId             业务标识
     * @return
     */
    boolean changeTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId);

    /**
     * 开通智慧经营业务
     *
     * @param merchantProviderParams
     * @param fee
     * @param sync
     * @param tradeAppId             业务标识
     * @return
     */
    boolean openSmartTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId);

    /**
     * 查询商户进件状态 默认不实现收单机构的进件状态,具体实现由子类完成,注意由于历史原因lkl查询不走这个,而通联进件直接回返回商户号
     *
     * @param contractSubTask 子任务
     * @return null
     * <AUTHOR>
     * @time 17:48
     */
    default HandleQueryStatusResp queryAndHandleContractStatus(ContractSubTask contractSubTask) {
        return null;
    }

    /**
     * 判断当前银行进件是否处于待签约状态
     *
     * @param contractSubTask
     * @return
     */
    default Boolean checkBankContractToBeSigned(ContractSubTask contractSubTask) {
        return Boolean.FALSE;
    }


    ContractResponse addTerminal(ContractTask task, ContractSubTask subTask);

    ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn);

    ContractResponse unbindTerminal(LogOutTermInfoDTO termInfoDTO, int payWay, String terminalSn);

    ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn);

    ContractResponse updateTerminalWithCustom(UpdateTermInfoDTO dto,
                                              int payWay,
                                              String terminalSn,
                                              AliTermInfoRequest customAliTermInfo,
                                              WxTermInfoRequest wxCustomInfo
    );

    /**
     * 收钱吧终端绑定 不同的通道处理不同的业务逻辑
     *
     * @param merchant 商户信息
     * @param terminal 收钱吧终端信息
     * @param provider 收单机构provider
     */
    default void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {
    }

    /**
     * 收钱吧终端解绑 不同的通道处理不同的业务逻辑
     *
     * @param merchant 商户信息
     * @param terminal 收钱吧终端信息
     * @param provider 收单机构provider
     */
    default void handleSqbTerminalUnBind(MerchantInfo merchant, Map terminal, Integer provider) {
    }

    /**
     * 获取商户级别大终端号
     *
     * @param merchantSn
     * @return
     */
    default String getMerchantTermNo(String merchantSn) {
        return "";
    }

    /**
     * 收钱吧门店级别终端绑定 不同的通道处理不同的业务逻辑
     *
     * @param storeSn
     * @param merchantSn
     * @param provider   收单机构provider
     */
    default void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {

    }

    /**
     * 收钱吧门店级别终端绑定 不同的通道处理不同的业务逻辑
     *
     * @param merchantSn
     * @param provider   收单机构provider
     */
    default void createProviderTerminal(String merchantSn, Integer provider) {

    }


    /**
     * 收钱吧商户级别终端绑定 不同的通道处理不同的业务逻辑
     *
     * @param merchantSn
     * @param provider   收单机构provider
     */
    default void handleSqbMerchantProviderTerminal(String merchantSn, Integer provider) {

    }

    default ContractResponse queryMerchantContractResult(String providerMerchantId) {
        throw new ContractBizException("子类还未实现收单机构商户报备查询逻辑");
    }

    default ContractResponse queryTermContractResult(String merchantSn, String termNo) {
        throw new ContractBizException("子类还未实现收单机构终端报备查询逻辑");
    }

    List getFeeRate(String merchantId);



    /**
     * 小微升级重新入网生成报备子任务
     *
     * @param merchantSn   商户号
     * @param paramContext 上下文信息
     * @param contractRule 规则
     * @param ruleGroupId
     * @return
     */
    ContractSubTask produceMicroUpgradeTaskByRule(String merchantSn,  Map<String, Object> paramContext, ContractRule contractRule,String ruleGroupId);



    /**
     * 处理小微升级报备子任务
     *
     * @param contractTask 任务
     * @param contractChannel 收单机构渠道信息
     * @param contractSubTask 子任务
     * @return
     */
    ContractResponse processMicroUpgradeTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask);

    /**
     * 子类处理小微升级报备逻辑
     *
     * @param contractTask 任务
     * @param contractChannel 收单机构渠道信息
     * @param subTask 子任务
     * @return
     */
    default ContractResponse doProcessMicroUpgradeTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask subTask){
        throw new ContractBizException("子类还未实现收单机构小微升级报备逻辑");
    }

}
