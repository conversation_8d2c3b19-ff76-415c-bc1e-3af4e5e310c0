package com.wosai.upay.job.biz.acquirePos;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 根据业务标识获取对应的处理类工厂
 * 使用双重检查锁和Guava Multimap优化性能
 */
@Component
@Slf4j
@DependsOn("applicationApolloConfig")
public class T9HandleFactory {
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private List<T9HandleService> t9HandleServiceList;

    private volatile boolean initialized = false;

    /**
     * 收单机构与处理服务映射
     */
    private final Map<String, T9HandleService> acquirerServiceMap = new ConcurrentHashMap<>();

    /**
     * 收单机构与终端标识的多重映射
     */
    private final Multimap<String, String> acquirerVendorMultimap = HashMultimap.create();

    /**
     * 终端标识与收单机构映射
     */
    private final Map<String, String> vendorAcquirerMap = new ConcurrentHashMap<>();

    /**
     * 业务标识与收单机构映射
     */
    private final Map<String, String> devCodeAcquirerMap = new ConcurrentHashMap<>();

    /**
     * 收单机构与业务标识映射
     */
    private final Map<String, String> acquirerDevCodeMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (initialized) {
            return;
        }
        synchronized (this) {
            if (initialized) {
                return;
            }
            try {
                // 初始化服务映射
                t9HandleServiceList.forEach(service -> {
                    String acquire = service.choseAcquire();
                    String devCode = service.getDevCode();
                    acquirerServiceMap.put(acquire, service);
                    devCodeAcquirerMap.put(devCode, acquire);
                    acquirerDevCodeMap.put(acquire, devCode);
                });

                // 初始化终端标识映射
                initializeVendorMappings();

                initialized = true;
                log.info("T9HandleFactory initialization completed successfully");
            } catch (Exception e) {
                log.error("Failed to initialize T9HandleFactory", e);
                throw e;
            }
        }
    }

    /**
     * 初始化终端标识相关映射
     */
    private void initializeVendorMappings() {
        // 初始化各收单机构的终端标识
        initializeAcquirerVendors(McConstant.ACQUIRER_LKLV3, applicationApolloConfig.getSimpleSuperPos());
        initializeAcquirerVendors(McConstant.ACQUIRER_FUYOU, applicationApolloConfig.getFySimpleSuperPos());
        initializeAcquirerVendors(McConstant.ACQUIRER_TONGLIANV2, applicationApolloConfig.getTlV2SimpleSuperPos());

        // 构建反向映射
        acquirerVendorMultimap.entries().forEach(entry -> 
            vendorAcquirerMap.put(entry.getValue(), entry.getKey())
        );
    }

    /**
     * 初始化单个收单机构的终端标识
     */
    private void initializeAcquirerVendors(String acquirer, List<String> vendors) {
        if (vendors != null && !vendors.isEmpty()) {
            vendors.forEach(vendor -> acquirerVendorMultimap.put(acquirer, vendor));
        }
    }

    /**
     * 根据终端标识获取处理服务
     */
    public T9HandleService getAcquirePosServiceByVenderAppAppId(String vendorAppId) {
        if (StringUtils.isEmpty(vendorAppId)) {
            throw new CommonPubBizException(PosConstant.UNKOWNER_VENDER_APP_ID);
        }
        String acquirer = vendorAcquirerMap.get(vendorAppId);
        if (StringUtils.isEmpty(acquirer)) {
            throw new CommonPubBizException(PosConstant.UNKOWNER_VENDER_APP_ID);
        }
        return acquirerServiceMap.get(acquirer);
    }

    /**
     * 根据收单机构获取处理服务
     */
    public T9HandleService getAcquirePosServiceByAcquire(String acquire) {
        if (StringUtils.isEmpty(acquire)) {
            throw new CommonPubBizException("未找到处理类");
        }
        return acquirerServiceMap.get(acquire);
    }

    /**
     * 根据业务标识获取处理服务
     */
    public T9HandleService getAcquirePosServiceByDevCode(String devCode) {
        if (StringUtils.isEmpty(devCode)) {
            throw new CommonPubBizException("未找到处理类");
        }
        return t9HandleServiceList.stream()
                .filter(handle -> handle.isSupport(devCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取收单机构的终端标识列表
     */
    public Collection<String> getVendorsByAcquirer(String acquirer) {
        return acquirerVendorMultimap.get(acquirer);
    }

    /**
     * 获取所有收单机构及其终端标识映射
     */
    public Map<String, Collection<String>> getAcquireVenderMap() {
        return acquirerVendorMultimap.asMap();
    }

    /**
     * 获取除指定收单机构外的所有其他收单机构对应的T9设备标识
     */
    public List<String> otherAcquireVenderList(String excludeAcquire) {
        return acquirerVendorMultimap.entries().stream()
                .filter(entry -> !Objects.equals(entry.getKey(), excludeAcquire))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前所有收单机构和对应devCode标识
     */
    public Map<String, String> getAcquirerDevCodeMap() {
        return Collections.unmodifiableMap(acquirerDevCodeMap);
    }

    /**
     * 获取当前所有终端标识与收单机构的映射关系
     */
    public Map<String, String> getVerderAcquireMap() {
        return Collections.unmodifiableMap(vendorAcquirerMap);
    }

    /**
     * @deprecated 请使用 getVendorsByAcquirer(McConstant.ACQUIRER_LKLV3)
     */
    @Deprecated
    public List<String> getLklT9PosList() {
        Collection<String> vendors = getVendorsByAcquirer(McConstant.ACQUIRER_LKLV3);
        return new ArrayList<>(vendors);
    }

    /**
     * @deprecated 请使用 getVendorsByAcquirer(McConstant.ACQUIRER_FUYOU)
     */
    @Deprecated
    public List<String> getFyT9PosList() {
        Collection<String> vendors = getVendorsByAcquirer(McConstant.ACQUIRER_FUYOU);
        return new ArrayList<>(vendors);
    }

    /**
     * @deprecated 请使用 getVendorsByAcquirer(McConstant.ACQUIRER_TONGLIANV2)
     */
    @Deprecated
    public List<String> getTlV2T9PosList() {
        Collection<String> vendors = getVendorsByAcquirer(McConstant.ACQUIRER_TONGLIANV2);
        return new ArrayList<>(vendors);
    }
}
