package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-04-09
 */
@Slf4j
public abstract class AbstractSubTaskHandler implements SubTaskHandler {

    @Autowired
    private ContractTaskMapper taskMapper;

    @Autowired
    private ContractSubTaskMapper subTaskMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    TaskResultService taskResultService;

    @Autowired
    MonitorLog monitorLog;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    protected ProviderFactory providerFactory;

    @Autowired
    ScheduleUtil scheduleUtil;

    @Autowired
    AddAffectStatusSuccessTaskCountBiz addAffectStatusSuccessTaskCountBiz;


    @Override
    @Timed(value = "SubTaskHandler")
    public void handle(ContractTask task, ContractSubTask subTask) throws Exception {
        try {
            task = taskMapper.selectByPrimaryKey(task.getId());
            subTask = subTaskMapper.selectByPrimaryKey(subTask.getId());
            AbstractSubTaskHandler handler = (AbstractSubTaskHandler) AopContext.currentProxy();
            handler.preHandle(task, subTask);
            handler.doHandle(task, subTask);
        } catch (Exception e) {
            String errorMsg = String.format("merchant: %s sub_task_id: %s error: %s", subTask.getMerchant_sn(), subTask.getId(), ExceptionUtil.getThrowableMsg(e));
            log.error("handleSubTask error {}", errorMsg, e);
            chatBotUtil.sendMessageToContractWarnChatBot(errorMsg);
            handleError(task, subTask, e);
        }
    }


    /**
     * 处理异常
     *
     * @param task
     * @param subTask
     * @param e
     * @throws Exception
     */
    protected abstract void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception;


    /**
     * 具体处理逻辑
     * <p>
     * 子类如果需要事务控制，需要加上  @Transactional
     *
     * @param task
     * @param subTask
     */
    @Transactional(rollbackFor = Exception.class)
    public abstract void doHandle(ContractTask task, ContractSubTask subTask);

    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    ContractSubTaskBiz contractSubTaskBiz;

    @Autowired
    BankCardServiceImpl bankCardService;

    protected void preHandle(ContractTask task, ContractSubTask subTask) {
        if (task.getStatus().equals(TaskStatus.PENDING.getVal())) {
//            contractTaskBiz.update(new ContractTask().setStatus(1).setId(task.getId()).setComplete_at(task.getComplete_at()));
            ContractTask updateTask = new ContractTask().setId(task.getId()).setComplete_at(task.getComplete_at());
            if (!Objects.equals(ProviderUtil.CONTRACT_TYPE_AUTH, task.getType())) {
                updateTask.setStatus(1);
            }
            contractTaskBiz.update(updateTask);
            if (contractSubTaskBiz.changeBankStatus(task, subTask)) {
                bankCardService.updateCardAfterTaskStatus(JSON.parseObject(task.getEvent_context(), Map.class), MerchantBankAccount.VERIFY_STATUS_INPROGRESS, "");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleResult(ContractResponse response, ContractSubTask subTask) {
        if (response == null) {
            log.info("AbstractSubTaskHandler, merchantSn {} subTask {} channelName {} processTask return null", subTask.getMerchant_sn(), subTask.getId(), subTask.getChannel());
            return;
        }
        ContractTask task = contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id());
        ContractSubTask update = new ContractSubTask()
                .setId(subTask.getId())
                .setResult(response.getMessage())
                .setResponse_body(JSON.toJSONString(response.getResponseParam()))
                .setPriority(new Date());
        //图片上传的请求因为编码的关系特别长，因此不赋值，详细内容可从task中查看；
        if (!ProviderUtil.SUB_TASK_TASK_TYPE_FILE_SUPPLY.equals(subTask.getTask_type())) {
            update.setRequest_body(JSON.toJSONString(response.getRequestParam()));
        }
        if (response.isSystemFail()) {
            ContractRule contractRule = ruleContext.getContractRule(subTask.getContract_rule());
            Integer retry = subTask.getRetry();
            if (retry > contractRule.getRetry()) {
                contractTaskMapper.updatePriorityAndResult(task.getId(),
                        LocalDateTime.now().plusMinutes(scheduleUtil.getQueryTime().getLklV3Delay()).format(formatter),
                        // TODO: 2021/12/30 当response 中什么都没有的时候 这样的插入会导致 task.result 中内容无法被前端展示
                        response.getMessage()
                );
                update.setRetry(++retry).setResult(response.getMessage());
            } else {
                update.setRetry(++retry).setResult(response.getMessage());
            }
        }
        // TODO: 2021/12/30 当response 中什么都没有的时候
        if (response.isBusinessFail()) {
            update.setStatus(SubTaskStatus.FAIL.getVal());
            if (1 == subTask.getStatus_influ_p_task()) {
                Map reMsg = CollectionUtil.hashMap("channel", subTask.getChannel(), "message", response.getMessage(), "result", response.getMessage());
                taskResultService.changeStatusAndResultV2(task.getId(), subTask.getId(), 6, JSON.toJSONString(reMsg), false);
            }
        }
        if (response.isSuccess()) {
            String contractId = MapUtils.getString(response.getTradeParam(), LakalaConstant.CONTRACTID);
            if (StringUtils.isEmpty(contractId)) {
                update.setStatus(SubTaskStatus.SUCCESS.getVal());
                if (subTask.getStatus_influ_p_task() == 1) {
                    addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(subTask.getP_task_id());
                }
                contractSubTaskMapper.setEnableScheduleByDepId(subTask.getId());
                taskResultService.changeStatusAndResultV2(task.getId(), subTask.getId(), contractTaskMapper.selectByPrimaryKey(task.getId()).getStatus(), null, false);
            } else {
                update.setContract_id(contractId).setStatus(SubTaskStatus.CONTRACT.getVal());
            }
        }
        contractSubTaskMapper.updateByPrimaryKey(update);
    }

}

