package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 收单机构切换到银联商务
 *
 * <AUTHOR>
 * @date 2020-04-26
 */
@Component("ums-AcquirerChangeBiz")
public class ChangeToUmsBiz extends AbstractIndirectAcquirerChangeBiz {


    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_UMS.getValue();
    }


    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_UMS_RULE_GROUP;
    }

    @Override
    protected void updateClearanceProvider(McAcquirerChange change) {
        tradeConfigService.updateClearanceProvider(change.getMerchant_id(), TransactionParam.CLEARANCE_PROVIDER_YS);
    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderIn(Arrays.asList(ProviderEnum.PROVIDER_UMS.getValue()))
                .andPaywayNotEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        return paramsMapper.selectByExampleWithBLOBs(example);
    }
}
