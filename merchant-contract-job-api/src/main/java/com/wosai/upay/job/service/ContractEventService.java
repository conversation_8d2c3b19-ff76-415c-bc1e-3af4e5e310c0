package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.FailEventConf;
import com.wosai.upay.job.model.NewMchNetInReq;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 报备事件服务，用于创建各种进件事件
 *
 * <AUTHOR>
 * @date 2019-09-09
 */
@JsonRpcService("/rpc/contractEvent")
@Validated
public interface ContractEventService {

    /**
     * 保存进件事件
     * 用于新商户入网
     *
     * @param merchantSn  商户号
     * @param ruleGroupId 规则组
     * @param platform    调用方
     * @return
     */
    ContractEvent saveContractEvent(String merchantSn, String ruleGroupId, String platform);

    /**
     * 保存进件事件
     * 用于新商户入网
     *
     * @param merchantSn 商户号
     * @param comboId    套餐ID
     * @param platform   调用方
     * @return
     * @deprecated since 2022.9.20 由于客户关系创建顺序发生变化 若新增商户入网调用此接口会查不到组织
     */
    @Deprecated
    ContractEvent saveContractEventV2(String merchantSn, Long comboId, String platform);

    /**
     * 保存进件事件
     * 用于新商户入网
     *
     * @param merchantSn 商户号
     * @param comboId    套餐ID
     * @param platform   调用方
     * @param failMsg    错误信息
     * @return
     * @deprecated since 2022.9.20 由于客户关系创建顺序发生变化 若新增商户入网调用此接口会查不到组织
     */
    @Deprecated
    ContractEvent saveFailEvent(String merchantSn, Long comboId, String platform, String failMsg);

    /**
     * 保存进件事件 用于新商户入网
     * @param req 请求参数
     * @return 进件事件
     */
    ContractEvent saveContractEventV3(@Valid NewMchNetInReq req);

    /**
     * 保存进件事件 用于新商户入网 命中黑名单
     * @param req 请求参数
     * @return 进件事件
     */
    ContractEvent saveFailEventV2(@Valid NewMchNetInReq req);

    /**
     * 同步信息至上游机构
     *
     * @param merchantSn
     * @param eventType  event类型
     *                   目前只支持 0 -> 商户信息变更; 1 -> 银行卡信息变更
     * @param platform
     * @return
     */
    ContractResponse refreshMerchant(String merchantSn, int eventType, String platform);

    /**
     * 同步银行卡信息至上游机构
     * 结算证件更新审批通过之后调用该接口，该审批调用该接口task处理过程不改变银行卡状态
     * @param merchantSn 商户号
     * @param platform   平台
     * @return 开通结果
     */
    ContractResponse refreshMerchantBankAccount(String merchantSn, String platform);

    /**
     * 重新调度该入网事件
     * @param merchantSn 商户号
     * @return 调度结果
     */
    ContractResponse rescheduleMultiProviderEvent(@NotBlank(message = "商户号不能为空") String merchantSn);


}
