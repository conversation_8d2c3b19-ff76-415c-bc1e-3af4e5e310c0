<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>merchant-contract-job</artifactId>
        <groupId>com.wosai.upay</groupId>
        <version>4.26.9-AT-SNAPSHOT</version>
    </parent>
    <groupId>com.wosai.upay</groupId>
    <artifactId>merchant-contract-job-api</artifactId>
    <packaging>jar</packaging>
    <version>4.26.9-AT-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-api</artifactId>
            <version>1.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.common</groupId>
            <artifactId>wosai-common</artifactId>
            <version>1.6.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-common</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-api</artifactId>
            <version>2.64.8-micro-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-contract-job-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shouqianba.workflow</groupId>
            <artifactId>sp-workflow-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-qrcode-api</artifactId>
            <version>1.4.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>merchant-contract-activity-api</artifactId>
            <version>1.21.11</version>
            <exclusions>
                <exclusion>
                    <artifactId>merchant-contract-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-contract-job-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>crm-databus</artifactId>
            <version>0.25.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>merchant-contract-job-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-databus-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-databus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>merchant-business-open-api</artifactId>
            <version>2.5.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.16</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.bsm</groupId>
            <artifactId>credit-pay-backend-api</artifactId>
            <version>1.1.64</version>
        </dependency>

        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>cua-common</artifactId>
            <version>0.3.24</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>scene-sdk</artifactId>
            <version>1.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-gateway-api</artifactId>
            <version>1.0.0</version>
        </dependency>

    </dependencies>
    <!--<distributionManagement>-->
    <!--<repository>-->
    <!--<id>deployment</id>-->
    <!--<name>Internal Releases</name>-->
    <!--<url>http://maven.wosai-inc.com/nexus/content/repositories/releases/</url>-->
    <!--</repository>-->
    <!--<snapshotRepository>-->
    <!--<id>deployment</id>-->
    <!--<name>Internal Snapshots</name>-->
    <!--<url>http://maven.wosai-inc.com/nexus/content/repositories/snapshots/</url>-->
    <!--</snapshotRepository>-->
    <!--</distributionManagement>-->

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>